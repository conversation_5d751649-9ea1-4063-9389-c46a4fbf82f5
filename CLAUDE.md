# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

本项目是一个量化交易系统，专门用于加密货币的仓位管理和交易。系统包含一个Web管理界面，用于控制交易程序、管理配置和监控系统状态。

## 常用命令

### 启动系统
```bash
# 启动主交易程序（实盘模式）
python startup.py

# 启动Web管理界面
python start_web_manager.py
# 或者手动启动
python web_manager.py

# 使用特定账户配置
export X3S_TRADING_ACCOUNT=账户名.py
python startup.py
```

### 依赖管理
```bash
# 安装依赖
pip install -r requirements.txt
```

### 配置管理
```bash
# 查看全局配置
cat config.py

# 查看启动配置
cat startup.json

# 查看用户配置
cat users_config.py
```

## 核心架构

### 主要模块结构

1. **startup.py** - 主启动脚本，负责整个交易系统的运行循环
   - 管理多账户配置文件的加载和执行
   - 处理5分钟周期的数据检查和交易执行
   - 支持调试模式和干运行模式

2. **config.py** - 全局配置中心
   - 数据路径配置（realtime_data_path）
   - 交易所API配置
   - 回测和模拟器参数设置
   - 支持通过环境变量X3S_TRADING_ACCOUNT指定账户

3. **web_manager.py** - Web管理界面
   - Flask + SocketIO实现的Web服务器
   - 支持程序启动/停止控制
   - 配置文件管理和编辑
   - 实时系统监控

4. **core/** - 核心交易引擎
   - **account_manager.py**: 账户管理和初始化
   - **real_trading.py**: 实盘交易执行
   - **backtest.py**: 回测系统
   - **binance/**: 币安交易所客户端封装
   - **model/**: 数据模型定义
   - **utils/**: 工具类集合

### 数据流架构

1. **数据获取**: 从实盘数据中心获取K线和市场数据
2. **因子计算**: 在factors/目录下定义的各种技术指标和因子
3. **策略执行**: 基于因子计算结果进行选币和仓位管理
4. **订单执行**: 通过binance客户端执行实际交易

### 配置系统

- **accounts/**: 存放各个交易账户的配置文件
- **config.py**: 全局系统配置
- **config.json**: JSON格式的配置覆盖（可选）
- **startup.json**: 启动参数配置

### 关键特性

1. **多账户支持**: 可同时管理多个交易账户，每个账户有独立的配置
2. **时间偏移机制**: 支持hour_offset配置，允许不同账户在不同时间执行
3. **调试模式**: is_debug=True时，系统模拟运行不执行实际交易
4. **干运行模式**: is_dry_run=True时，只打印下单信息但不实际执行
5. **增量缓存**: 支持选币结果和策略数据的缓存机制
6. **Web界面**: 提供完整的Web管理界面用于系统控制

### 重要路径

- **数据路径**: `/opt/coin-realtime-data_v1.1.3/data`（实盘）
- **缓存路径**: `data/runtime/`
- **回测结果**: `data/仓位管理回测结果/`
- **快照存储**: `data/snapshot/`

### 安全注意事项

1. 所有敏感的API密钥配置应放在accounts/目录下的配置文件中
2. 系统支持企业微信机器人通知，通过error_webhook_url配置
3. Web界面默认绑定所有网络接口，生产环境需要配置访问控制

### 开发提示

- 系统主要使用Python，核心依赖包括pandas、ccxt、Flask等
- 因子开发在factors/目录下，按照现有模式编写
- 策略配置通过strategy_config字典进行
- 日志系统使用core/utils/log_kit.py
- 所有路径操作统一使用core/utils/path_kit.py

### 运行模式

- **实盘模式**: is_debug=False，连接真实交易所执行交易
- **调试模式**: is_debug=True，模拟运行用于开发测试
- **干运行模式**: is_dry_run=True，打印交易信息但不实际下单